"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { useProducts } from "@/lib/product-provider"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"

interface NewProfessionalProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NewProfessionalProductDialog({ open, onOpenChange }: NewProfessionalProductDialogProps) {
  const { currentLocation } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  const { categories, productTypes, addProduct } = useProducts()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    // Basic Information
    name: "",
    sku: "",
    barcode: "",
    category: "",
    type: "",
    description: "",

    // Pricing
    costPrice: "",

    // Inventory
    initialStock: "0",
    minStockLevel: "0",
    maxStockLevel: "0",

    // Status
    isActive: true,
  })

  const handleChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate required fields
      if (!formData.name || !formData.category) {
        toast({
          variant: "destructive",
          title: "Validation Error",
          description: "Please fill in all required fields (Name and Category).",
        })
        return
      }

      // Create enhanced product data
      const productData = {
        ...formData,
        location: currentLocation,
        // Convert string prices to numbers
        costPrice: formData.costPrice ? parseFloat(formData.costPrice) : 0,
        initialStock: parseInt(formData.initialStock) || 0,
        minStockLevel: parseInt(formData.minStockLevel) || 0,
        maxStockLevel: parseInt(formData.maxStockLevel) || 0,
        // Add timestamps
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Create the product using the product provider
      const newProduct = addProduct({
        name: formData.name,
        sku: formData.sku,
        barcode: formData.barcode,
        category: formData.category,
        type: formData.type,
        description: formData.description,
        price: 0, // Professional products don't have retail price
        salePrice: undefined,
        cost: productData.costPrice,
        stock: productData.initialStock,
        minStock: productData.minStockLevel,
        isRetail: false, // Professional products are not retail
        isActive: formData.isActive,
        isFeatured: false,
        isNew: false,
        isBestSeller: false,
        isSale: false,
        image: "https://images.unsplash.com/photo-1608248597279-f99d160bfcbc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [],
        features: [],
        ingredients: [],
        howToUse: [],
        location: currentLocation,
      })

      console.log("Professional product created:", newProduct)

      toast({
        title: "Professional product created successfully",
        description: `${formData.name} has been added to your professional inventory.`,
      })

      onOpenChange(false)
      resetForm()
    } catch (error) {
      console.error("Failed to create professional product:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create professional product. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      sku: "",
      barcode: "",
      category: "",
      type: "",
      description: "",
      costPrice: "",
      initialStock: "0",
      minStockLevel: "0",
      maxStockLevel: "0",
      isActive: true,
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Add Professional Product</DialogTitle>
            <DialogDescription>
              Create a new product for professional salon use. These products will not appear in the client shop.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                  placeholder="Enter product name"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sku">SKU</Label>
                  <Input
                    id="sku"
                    value={formData.sku}
                    onChange={(e) => handleChange("sku", e.target.value)}
                    placeholder="Product SKU (optional)"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="barcode">Barcode</Label>
                  <Input
                    id="barcode"
                    value={formData.barcode}
                    onChange={(e) => handleChange("barcode", e.target.value)}
                    placeholder="Product barcode (optional)"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleChange("category", value)}>
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories
                        .filter(cat => cat.isActive)
                        .map((category) => (
                          <SelectItem key={`category-${category.id}`} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Product Type</Label>
                  <Select value={formData.type} onValueChange={(value) => handleChange("type", value)}>
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {productTypes
                        .filter(type => type.isActive)
                        .map((type) => (
                          <SelectItem key={`type-${type.id}`} value={type.name}>
                            {type.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  rows={3}
                  placeholder="Product description (optional)"
                />
              </div>

              {/* Pricing */}
              <div className="space-y-2">
                <Label htmlFor="costPrice">Cost Price</Label>
                <Input
                  id="costPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.costPrice}
                  onChange={(e) => handleChange("costPrice", e.target.value)}
                  placeholder="0.00"
                />
              </div>

              {/* Inventory */}
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="initialStock">Initial Stock</Label>
                  <Input
                    id="initialStock"
                    type="number"
                    min="0"
                    value={formData.initialStock}
                    onChange={(e) => handleChange("initialStock", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minStockLevel">Min Stock Level</Label>
                  <Input
                    id="minStockLevel"
                    type="number"
                    min="0"
                    value={formData.minStockLevel}
                    onChange={(e) => handleChange("minStockLevel", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxStockLevel">Max Stock Level</Label>
                  <Input
                    id="maxStockLevel"
                    type="number"
                    min="0"
                    value={formData.maxStockLevel}
                    onChange={(e) => handleChange("maxStockLevel", e.target.value)}
                  />
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleChange("isActive", checked)}
                />
                <Label htmlFor="isActive">Active (available for use)</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Professional Product"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
