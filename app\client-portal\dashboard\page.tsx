"use client"

import { useState, useEffect, useMemo } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { useClients } from "@/lib/client-provider"
import { useServices } from "@/lib/service-provider"
import { useCurrency } from "@/lib/currency-provider"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { ClientPortalLayout } from "@/components/client-portal/client-portal-layout"
import { PersonalizedRecommendations } from "@/components/client-portal/personalized-recommendations"
import { useGlobalCurrencyChange } from "@/components/global-currency-enforcer"
import {
  Calendar,
  Clock,
  ShoppingBag,
  User,
  CreditCard,
  Heart,
  Star,
  Gift,
  Bell,
  Scissors,
  ChevronRight,
  ArrowRight,
  Plus
} from "lucide-react"
import { format } from "date-fns"
import { mockAppointments, mockServices } from "@/lib/mock-data"

export default function ClientDashboardPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { clients, getClient } = useClients()
  const { services, categories, getCategoryName } = useServices()
  const { currencyCode, formatCurrency } = useCurrency()
  const [client, setClient] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [forceUpdate, setForceUpdate] = useState(0)

  // Listen for currency changes to ensure consistent currency display
  useGlobalCurrencyChange((newCurrencyCode) => {
    console.log(`Currency changed in client dashboard: ${newCurrencyCode}`)
    // Force a re-render when currency changes
    setForceUpdate(prev => prev + 1)
  })

  // Check if client is authenticated
  useEffect(() => {
    const token = localStorage.getItem("client_auth_token")
    const clientEmail = localStorage.getItem("client_email")
    const clientId = localStorage.getItem("client_id")

    if (!token || !clientEmail) {
      toast({
        title: "Authentication required",
        description: "Please sign in to access your dashboard",
        variant: "destructive",
      })
      router.push("/client-portal")
      return
    }

    // Find client by email or ID
    let foundClient
    if (clientId) {
      foundClient = getClient(clientId)
    } else {
      foundClient = clients.find(c => c.email === clientEmail)
    }

    if (foundClient) {
      setClient(foundClient)
    } else {
      // If client not found, create a mock client for demo purposes
      setClient({
        id: "client123",
        name: "Jane Smith",
        email: clientEmail,
        phone: "(*************",
        preferredLocation: "loc1",
        avatar: "JS",
        loyaltyPoints: 450,
        memberSince: "January 2025"
      })
    }

    setLoading(false)
  }, [clients, getClient, router, toast])

  // Get upcoming appointments for this client
  const upcomingAppointments = mockAppointments
    .filter(appointment =>
      appointment.clientId === (client?.id || "client123") &&
      new Date(appointment.date) > new Date()
    )
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 3)

  // Get past appointments for this client
  const pastAppointments = useMemo(() =>
    mockAppointments
      .filter(appointment =>
        appointment.clientId === (client?.id || "client123") &&
        new Date(appointment.date) < new Date()
      )
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3)
  , [client?.id])

  // Memoize client preferences to prevent unnecessary re-renders
  const clientPreferences = useMemo(() => ({
    preferredServices: ["Haircut & Style", "Color & Highlights"],
    preferredProducts: ["Hydrating Shampoo", "Styling Mousse"],
    preferredStylists: ["Emma Johnson"]
  }), [])

  // Mock recommended services based on past appointments
  const recommendedServices = useMemo(() =>
    mockServices
      .slice(0, 4)
      .map(service => ({
        ...service,
        image: `/service-${Math.floor(Math.random() * 5) + 1}.jpg`
      }))
  , [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600"></div>
      </div>
    )
  }

  return (
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16 bg-pink-100 text-pink-800">
              <AvatarFallback>{client?.avatar || client?.name?.split(" ").map((n: string) => n[0]).join("") || "C"}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">Welcome back, {client?.name?.split(" ")[0] || "Client"}!</h1>
              <p className="text-gray-600">Member since {client?.memberSince || "January 2025"}</p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <Link href="/client-portal/profile">
                <User className="mr-2 h-4 w-4" />
                My Profile
              </Link>
            </Button>
            <Button className="bg-pink-600 hover:bg-pink-700" asChild>
              <Link href="/client-portal/appointments/book">
                <Calendar className="mr-2 h-4 w-4" />
                Book Appointment
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Link href="/client-portal/appointments/book">
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <Calendar className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">Book Appointment</h3>
                <p className="text-sm text-gray-500">Schedule your next visit</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/client-portal/services">
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <Scissors className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">Our Services</h3>
                <p className="text-sm text-gray-500">{services.length} services available</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/client-portal/shop">
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <ShoppingBag className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">Shop Products</h3>
                <p className="text-sm text-gray-500">Browse our collection</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/client-portal/loyalty">
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <Gift className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">Loyalty Program</h3>
                <p className="text-sm text-gray-500">View your points & rewards</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/client-portal/reviews">
            <Card className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-3 group-hover:bg-pink-200 transition-colors">
                  <Star className="h-6 w-6" />
                </div>
                <h3 className="font-medium group-hover:text-pink-600 transition-colors">My Reviews</h3>
                <p className="text-sm text-gray-500">Share your feedback</p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Upcoming Appointments */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle>Upcoming Appointments</CardTitle>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/client-portal/appointments" className="text-pink-600">
                      View All
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {upcomingAppointments.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-pink-600 flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                      <Calendar className="h-8 w-8" />
                    </div>
                    <h3 className="font-medium mb-2">No upcoming appointments</h3>
                    <p className="text-gray-500 mb-4">Schedule your next visit with us</p>
                    <Button className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 shadow-md transition-all hover:shadow-lg" asChild>
                      <Link href="/client-portal/appointments/book">
                        <Calendar className="mr-2 h-4 w-4" />
                        Book Now
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {upcomingAppointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                        <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mr-4">
                          <Scissors className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{appointment.service}</h4>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1" />
                              {format(new Date(appointment.date), "MMMM d, yyyy")}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-3.5 w-3.5 mr-1" />
                              {format(new Date(appointment.date), "h:mm a")}
                            </div>
                            <div className="flex items-center">
                              <User className="h-3.5 w-3.5 mr-1" />
                              {appointment.staffName}
                            </div>
                          </div>
                        </div>
                        <Badge className={
                          appointment.status === "confirmed" ? "bg-green-100 text-green-800" :
                          appointment.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                          "bg-gray-100 text-gray-800"
                        }>
                          {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                        </Badge>
                      </div>
                    ))}
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/client-portal/appointments/book">
                        <Plus className="mr-2 h-4 w-4" />
                        Book Another Appointment
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Personalized Recommendations */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Recommended For You</CardTitle>
                <CardDescription>Personalized recommendations based on your preferences and history</CardDescription>
              </CardHeader>
              <CardContent>
                <PersonalizedRecommendations
                  clientId={client?.id || "client123"}
                  clientPreferences={clientPreferences}
                  pastAppointments={pastAppointments}
                />
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pastAppointments.map((appointment) => (
                    <div key={appointment.id} className="flex items-start p-3 rounded-lg border">
                      <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mr-3">
                        <Scissors className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <h4 className="font-medium">{appointment.service}</h4>
                          <span className="text-sm text-gray-500">
                            {format(new Date(appointment.date), "MMM d, yyyy")}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500">with {appointment.staffName}</p>
                        <div className="mt-2 flex justify-between items-center">
                          <Badge variant="outline" className="bg-gray-50">
                            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                          </Badge>
                          <Button variant="ghost" size="sm" className="text-pink-600 h-8">
                            Book Again
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Loyalty Card */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Loyalty Program</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg p-4 text-white">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <p className="text-white/80 text-sm">Vanity Hub</p>
                      <h3 className="font-bold text-lg">Rewards Card</h3>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-5 w-5 fill-white text-white" />
                      <span className="font-bold ml-1">{client?.loyaltyPoints || 450} pts</span>
                    </div>
                  </div>
                  <p className="text-sm text-white/80 mb-1">Member Name</p>
                  <p className="font-medium">{client?.name || "Jane Smith"}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress to next reward</span>
                    <span>{client?.loyaltyPoints || 450}/500 points</span>
                  </div>
                  <Progress value={((client?.loyaltyPoints || 450) / 500) * 100} className="h-2" />
                  <p className="text-xs text-gray-500">
                    Earn 50 more points for a <CurrencyDisplay amount={25} /> reward
                  </p>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">Available Rewards</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-2 rounded-lg border">
                      <div>
                        <p className="font-medium"><CurrencyDisplay amount={10} /> off your next service</p>
                        <p className="text-sm text-gray-500">Valid until Dec 31, 2025</p>
                      </div>
                      <Button size="sm" variant="outline">Redeem</Button>
                    </div>
                    <div className="flex justify-between items-center p-2 rounded-lg border">
                      <div>
                        <p className="font-medium">Free product sample</p>
                        <p className="text-sm text-gray-500">Valid until Nov 15, 2025</p>
                      </div>
                      <Button size="sm" variant="outline">Redeem</Button>
                    </div>
                  </div>
                </div>

                <Button variant="outline" className="w-full" asChild>
                  <Link href="/client-portal/loyalty">
                    View Loyalty Program
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Featured Services */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle>Featured Services</CardTitle>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/client-portal/services" className="text-pink-600">
                      View All
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {services.slice(0, 3).map((service) => (
                    <div key={service.id} className="flex items-center gap-3 group cursor-pointer">
                      <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 flex-shrink-0">
                        <Scissors className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium group-hover:text-pink-600 transition-colors">{service.name}</h4>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-gray-500">{getCategoryName(service.category)}</p>
                          {(service.showPrices ?? true) && (
                            <p className="font-medium"><CurrencyDisplay amount={service.price} /></p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/client-portal/services">
                      <Scissors className="mr-2 h-4 w-4" />
                      Explore All Services
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Featured Products */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Featured Products</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center gap-3 group cursor-pointer">
                      <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                        <Image
                          src={`/product-${i}.jpg`}
                          alt={`Product ${i}`}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium group-hover:text-pink-600 transition-colors">Premium Shampoo</h4>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-gray-500">For all hair types</p>
                          <p className="font-medium"><CurrencyDisplay amount={24.99} /></p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/client-portal/shop">
                      Shop All Products
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Special Offers */}
            <Card className="bg-gradient-to-br from-pink-50 to-purple-50 border-pink-100">
              <CardContent className="pt-6">
                <div className="text-center mb-4">
                  <Badge className="bg-pink-100 text-pink-800 mb-2">Limited Time</Badge>
                  <h3 className="text-xl font-bold mb-2">Summer Special Offer</h3>
                  <p className="text-gray-600 mb-4">
                    Get 20% off any color service when you book this month!
                  </p>
                  <Button className="bg-pink-600 hover:bg-pink-700">
                    Book Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
  )
}
